import { game<PERSON>elper } from "../../common/helper/GameHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class TimeStoneUseCommonPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://title_l
    protected detailNode_: cc.Node = null // path://detail_n
    protected costNode_: cc.Node = null // path://cost_n
    protected confirmNode_: cc.Node = null // path://confirm_be_n
    //@end

    _showArgs: { desc: string, callback: () => Promise<boolean>, cost: number } = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data: any) {
        this._showArgs = {
            desc: data.desc,
            cost: data.cost,
            callback: data.callback
        }
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://confirm_be_n
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        this._showArgs?.callback().then(r => r && this.close())
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.detailNode_.setLocaleKey(this._showArgs.desc)
        this.updateCost()
    }

    private updateCost() {
        const need = this._showArgs.cost
        const max = gameHelper.timeStone.getEnergy()
        this.costNode_.Child("lbl", cc.Label).string = `${need}/${max}`
        this.costNode_.Child("lbl", cc.MultiColor).setColor(need > max)
    }
}
