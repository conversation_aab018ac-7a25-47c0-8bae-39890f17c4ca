import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { ExtractData } from "../../model/jackpot/JackpotModel";

const { ccclass } = cc._decorator;

type Data = {
    id: number,
    selected: boolean
    evt: number
}

@ccclass
export default class TimeStoneUseJackpotPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://title_l
    protected detailNode_: cc.Node = null // path://detail_n
    protected costNode_: cc.Node = null // path://cost_n
    protected confirmNode_: cc.Node = null // path://confirm_be_n
    protected contentNode_: cc.Node = null // path://content_n
    //@end

    private data: Data[] = []

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data: ExtractData[]) {
        this.data = data.map((item, index) => ({ id: item.id, selected: false, evt: data.length - index - 1 }))
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://confirm_be_n
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        this.handle()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async handle() {
        const evts = []
        const ids = []
        this.data.forEach((item, index) => {
            if (item.selected) {
                evts.push(item.evt)
                ids.push(index)
            }
        })
        if (evts.length == 0) return void viewHelper.showAlert('timeStone_play_tips_0')
        const r = await eventCenter.req(EventType.TIMESTONE_USE, evts, proto.TimeStoneEvent.TypeJackpot, ids)
        if (!r) return
        this.close()
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.contentNode_.active = this.data.length != 1
        if (this.contentNode_.active) {
            this.contentNode_.Items(this.data, this.setOne.bind(this))
            this.detailNode_.active = true
        }
        this.updateCost()
    }

    private setOne(it: cc.Node, data: Data) {
        it.Child("gou").active = data.selected
        resHelper.loadRoleCircleIcon(data.id, it.Child("val", cc.Sprite), this.getTag())
        it.off("click")
        it.on("click", () => {
            data.selected = !data.selected
            it.Child("gou").active = data.selected
            this.updateCost()
        })
    }

    private updateCost() {
        const cost = assetsMgr.getJsonData<any>("TimeStoneEvent", proto.TimeStoneEvent.TypeJackpot).cost
        const need = this.data.filter(item => item.selected).length * cost
        const max = gameHelper.timeStone.getEnergy()
        this.costNode_.Child("lbl", cc.Label).string = `${need}/${max}`
        this.costNode_.Child("lbl", cc.MultiColor).setColor(need > max)
    }

}
