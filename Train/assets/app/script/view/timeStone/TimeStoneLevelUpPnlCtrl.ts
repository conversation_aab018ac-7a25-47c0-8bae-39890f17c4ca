import { TimeStoneCfg } from "../../common/constant/DataType";
import { ConditionType } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import { uiHelper } from "../../common/helper/UIHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class TimeStoneLevelUpPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://title_l
    protected lvNode_: cc.Node = null // path://lv_n
    protected attrNode_: cc.Node = null // path://attr_n
    protected costNode_: cc.Node = null // path://cost_n
    protected confirmNode_: cc.Node = null // path://confirm_be_n
    //@end

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data: any) {
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://confirm_be_n
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        gameHelper.timeStone.lvUp().then(r => r && this.close())
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    initView() {
        this.updateCost()
        this.updateLv()
        this.updateAttr()
    }

    private updateLv() {
        const lv = gameHelper.timeStone.getLv()
        this.setOne(lv, lv + 1, "common_guiText_11", "common_guiText_11", this.lvNode_)
    }

    private setOne(beforeVal: number, afterVal: number, beforeLang: string, afterLang: string, it: cc.Node) {
        it.Child("a").setLocaleKey(beforeLang, beforeVal)
        it.Child("2").active = false
        it.Child("b").active = false
        if (afterVal > beforeVal) {
            it.Child("2").active = true
            it.Child("b").active = true
        }
        if (afterLang) {
            it.Child("b").setLocaleKey(afterLang, afterVal)
        }
        else {
            it.Child("b", cc.Label).string = afterVal + ""
        }
    }

    private updateAttr() {
        const data = gameHelper.timeStone
        const nextJson = assetsMgr.getJsonData<TimeStoneCfg>("TimeStone", data.getLv() + 1)
        const attr = []
        attr.push({
            beforeVal: data.getMaxEnergy(),
            afterVal: nextJson ? nextJson.energy : 0,
            beforeLang: "timeStone_guiText_9",
            afterLang: ""
        })
        attr.push({
            beforeVal: data.getDailyRecover(),
            afterVal: nextJson ? nextJson.dailyRecover : 0,
            beforeLang: "timeStone_guiText_10",
            afterLang: ""
        })
        this.attrNode_.Items(attr, (it, data) => {
            this.setOne(data.beforeVal, data.afterVal, data.beforeLang, data.afterLang, it)
        })

    }

    private updateCost() {
        const cost = gameHelper.timeStone.getLvUpCost()
        if (!cost) return void (this.costNode_.active = false)
        this.costNode_.Items(cost, (it, data) => {
            let hav = gameHelper.getNumByCondition(gameHelper.toCondition(data))
            if (data.type == ConditionType.STAR_DUST) {
                it.Child('icon').Swih('star')
            } else {
                it.Child('icon').Swih('suipian')
            }
            it.Child('num', cc.Label).string = `${ut.simplifyMoney(hav)}/${uiHelper.getShowNum(data)}`
            it.Child('num', cc.MultiColor).setColor(hav < data.num)
        })
    }

}
