import { anim<PERSON><PERSON><PERSON> } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import EventType from "../../common/event/EventType";
import PassengerModel from "../../model/passenger/PassengerModel";
import RoleAvatarCmpt from "../role/RoleAvatarCmpt";
import { resHelper } from "../../common/helper/ResHelper";
import { ExtractData } from "../../model/jackpot/JackpotModel";
import ConditionObj from "../../model/common/ConditionObj";
import { ConditionType } from "../../common/constant/Enums";
import { viewHelper } from "../../common/helper/ViewHelper";
import TimeStoneBtnCmpt from "../cmpt/common/TimeStoneBtnCmpt";
import FrameIconNum from "../prefab/FrameIconNum";

const { ccclass } = cc._decorator;

let TimeAct = 1//进度条填充时间
let TimeDelay = 0.2//开始填充前等待时间
let TimeFragmentDelay = 0.2//碎片转换等待时间
const ary = ['', 'purple', 'gold']
@ccclass
export default class JackPotResultWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected oneNode_: cc.Node = null // path://one_n
    protected tenNode_: cc.Node = null // path://ten_n
    protected bottomNode_: cc.Node = null // path://bottom_n
    protected bagNode_: cc.Node = null // path://bag_n
    protected timeStoneNode_: cc.Node = null // path://time_stone_n
    protected currencyLayoutNode_: cc.Node = null // path://currency_layout_n
    //@end

    private timeRef = 0
    private dicProp = {}
    private _fragmentRef = 0
    private _extract: ExtractData[] = null
    private _waitResolve: Function = null

    public listenEventMaps() {
        return [
            { [EventType.TIMESTONE_USE_JACKPOT]: this.onTimeStoneUseJackpot }
        ]
    }

    public async onCreate() {
        this.node.active = false
        this.bottomNode_.active = false
        this.currencyLayoutNode_.active = false
    }

    public async init(ary: ExtractData[]) {
        this.timeStoneNode_.Component(TimeStoneBtnCmpt).initJackpot(ary)
        this._extract = ary
        this.timeRef = 0
        this.node.Child("title").opacity = 255
        this.oneNode_.opacity = 255
        this.tenNode_.opacity = 255
        this.node.active = true
        this.bagNode_.active = false
        this.calculateProp(ary)
        if (ary.length == 1) {
            this.showOne(ary[0])
        } else {
            this.showTen(ary)
        }
        await this.waitClose()
        await this.showEffect()
        this.node.active = false
        eventCenter.emit(EventType.GUIDE_JACKPOT_END4)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private calculateProp(ary: ExtractData[]) {
        let id = 0, dic = this.dicProp = {}
        ary.forEach(data => {
            if (data.convert) {
                id = data.id
                dic[id] = (dic[id] || 0) + data.convert
                this._fragmentRef++
            }
        })
    }
    private async showOne(data: ExtractData) {
        this.oneNode_.active = true
        this.tenNode_.active = false
        this.setItem(this.oneNode_, data)
    }
    private showTen(ary: ExtractData[]) {
        this.tenNode_.active = true
        this.oneNode_.active = false
        this.tenNode_.Items(ary, this.setItem.bind(this))
    }
    private setItem(item: cc.Node, data: ExtractData) {
        let model = gameHelper.passenger.getPassenger(data.id)
        item.Data = model
        item.opacity = 255
        item.Child("FrameIconNum").active = false
        this.setNodeNew(item.Swih('new')[0], model, !data.convert)
    }
    private setNodeSupian(node: cc.Node, model: PassengerModel, convertNum) {
        this.setNodeProgress(node.Child('progress'), model)
        let fragCfg = cfgHelper.getFragByCharacterIdAndQuality(String(model.id), model.quality)
        node.Child('avatar/RoleAvatar', RoleAvatarCmpt).init(fragCfg)
        node.Child('count', cc.Label).string = `x${convertNum}`
        this.setSuipianTx(node, model)
    }
    private setSuipianTx(node: cc.Node, model: PassengerModel) {
        let tx = node.Child('avatar/tx')
        let ary = ['', 'purple', 'gold']
        let ani = ary[model.quality - 1]
        let bol = !!ani
        tx.active = bol
        if (bol) {
            tx.Component(sp.Skeleton).playAnimation(ani, true)
        }
    }
    private async setNodeNew(node: cc.Node, model: PassengerModel, isNew: boolean = false) {
        let quality = model.cfg.quality - 1
        let nameNode = node.Child('name')
        let txAnis = ['blue', 'purple', 'gold']
        node.Child('tx', sp.Skeleton).playAnimation(txAnis[quality], true)
        node.Child('new').active = isNew
        node.Child('avatar').active = false
        nameNode.Component(cc.Label).setLocaleKey(model.name)
        nameNode.Component(cc.MultiColor).setColor(quality)
        const spN = node.Child('sp')
        spN.scale = .78
        spN.active = true
        await resHelper.loadRoleSp(model.id, spN, this.getTag(), false)
        node.Child('up').active = !isNew && model.isCanStarUp()
        if (!isNew) {
            this._fragmentRef--
        }
    }
    private setNodeProgress(node: cc.Node, model: PassengerModel) {
        let bolMax = model.isMaxStarLv()
        if (bolMax) {
            node.Component(cc.ProgressBar).progress = 1
            node.Child('bar').active = false
            node.Child('up').active = false
            node.Child('num').active = false
            node.Child('max').active = true
        } else {
            node.Child('bar').active = true
            node.Child('num').active = true
            node.Child('max').active = false
            this.actProgress(node, model)
        }
    }
    private setProgressNum(node: cc.Node, y: number, x: number) {
        node.Child('num', cc.Label).string = `${x}/${y}`
    }
    private setProgressBar(node: cc.Node, per: number) {
        let bol = per >= 1
        node.Component(cc.ProgressBar).progress = bol ? 1 : per
        node.Child('bar', cc.MultiFrame).setFrame(bol ? 1 : 0)
        node.Child('up').active = bol
    }
    private async actProgress(node: cc.Node, model: PassengerModel) {
        let id = model.id
        let need = cfgHelper.getStarLvCfg(model.getStarLv()).upCost
        let fragId = cfgHelper.getFragByCharacterIdAndQuality(String(model.getID()), model.quality).id
        let max = gameHelper.passenger.getFragCountById(+fragId)
        let add = this.dicProp[id]
        let min = max - add
        let per1 = 0
        let per2 = min / need
        let time = 0
        let cmpt = node.Component(cc.Sprite)
        let timeMax = add / need * TimeAct
        let call = (dt: number) => {
            time += dt
            if (time >= timeMax) {
                time = timeMax
                cmpt.unschedule(call)
                this.timeRef--
            }
            per1 = time / TimeAct
            this.setProgressBar(node, per1 + per2)
            per1 = time / timeMax
            this.setProgressNum(node, need, Math.floor(cc.misc.lerp(min, max, per1)))
        }
        call(0)
        this.timeRef++
        await ut.wait(TimeDelay, this)
        cmpt.scheduleUpdate(call)
    }
    private playActBottom() {
        this.bottomNode_.active = true
        animHelper.playContinueBlink(this.bottomNode_)
    }
    private stopActBottom(fun: Function) {
        this.bottomNode_.active = false
        cc.Tween.stopAllByTarget(this.bottomNode_)
        fun()
    }
    private async waitClose() {
        await this.waitProgressEnd()
        await this.playFragment()
        this.playActBottom()
        return new Promise(resolve => {
            this._waitResolve = resolve
            this.node.once(cc.Node.EventType.TOUCH_END, () => {
                this.stopActBottom(resolve)
            }, this)
        })
    }
    private async waitProgressEnd() {
        await ut.wait(TimeDelay, this)
        return new Promise(resolve => {
            let call = () => {
                if (this.timeRef == 0) {
                    this.unschedule(call)
                    resolve(null)
                }
            }
            this.scheduleUpdate(call)
        })
    }

    async playFragment() {
        await ut.wait(TimeFragmentDelay, this)
        await new Promise(resolve => {
            let call = () => {
                if (this._fragmentRef <= 0) {
                    this.unschedule(call)
                    resolve(null)
                }
            }
            this.scheduleUpdate(call)
        })
        const nodes: cc.Node[] = []
        if (this.oneNode_.active) {
            nodes.push(this.oneNode_)
        }
        if (this.tenNode_.active) {
            nodes.push(...this.tenNode_.children)
        }
        const promises: Promise<void>[] = []
        nodes.forEach((it, index) => {
            if (!this._extract[index].convert) return
            const data = it.Data as PassengerModel
            it = it.Child("new")
            const spN = it.Child("sp")
            const avatarN = it.Child("avatar")
            let fragCfg = cfgHelper.getFragByCharacterIdAndQuality(String(data.id), data.cfg.quality)
            avatarN.Child('RoleAvatar', RoleAvatarCmpt).init(fragCfg)
            const txN = avatarN.Child('tx')
            let ani = ary[data.cfg.quality - 1]
            let bol = !!ani
            txN.active = bol
            if (bol) {
                txN.Component(sp.Skeleton).playAnimation(ani, true)
            }
            cc.Tween.stopAllByTarget(spN)
            promises.push(cc.tween(spN).to(0.2, { scale: 0 }).call(() => {
                spN.active = false
                avatarN.active = true
            }).start().promise())

        })
        await Promise.all(promises)
    }

    async showEffect() {
        this.bagNode_.active = true
        this.currencyLayoutNode_.active = true
        const nodes: cc.Node[] = []
        const canvasNode = cc.find("Canvas")
        this._extract.forEach((e, index) => {
            if (!e.convert) return
            let it = null
            if (this._extract.length == 1) {
                it = this.oneNode_
            }
            else {
                it = this.tenNode_.children[index]
            }
            if (it.opacity == 0) return
            nodes.push(it)
        })
        if (!nodes.length) return
        const bagPos = this.bagNode_.getPosition()
        const diamondPos = this.currencyLayoutNode_.Child("DiamondUI").getPosition()
        const create = (node: cc.Node) => {
            const newNode = node.Child("new")
            const itemNode = node.Child("FrameIconNum")
            let aniNode: cc.Node = null
            let toParent: cc.Node = null
            let toPos: cc.Vec2 = null
            if (itemNode.active) {
                aniNode = itemNode
                toParent = this.currencyLayoutNode_
                toPos = diamondPos
            }
            else {
                aniNode = newNode.Child("avatar")
                toParent = this.bagNode_.parent
                toPos = bagPos
            }
            const centerPos = ut.convertToNodeAR(aniNode, canvasNode)
            const it = cc.instantiate2(aniNode, canvasNode)
            it.setPosition(centerPos)
            ut.convertParent(it, toParent)
            return [
                animHelper.flyPassengerHeart(it, toPos),
                cc.tween(it).to(.2, { scale: .6 }).start().promise(),
            ]
        }
        // 先创建
        const promises = nodes.map(node => create(node))
        // 隐藏界面的其他无关组件
        await Promise.all([
            cc.tween(this.node.Child("title")).to(.2, { opacity: 0 }).start().promise(),
            cc.tween(this.oneNode_).to(.2, { opacity: 0 }).start().promise(),
            cc.tween(this.tenNode_).to(.2, { opacity: 0 }).start().promise(),
        ])
        await ut.wait(30, this)
        // 播放碎片飞背包动画
        await ut.promiseMap(promises, promise => Promise.all(promise))
        animHelper.scaleBag(this.bagNode_)
        await ut.wait(0.3, this)
        this.bagNode_.active = false
        this.currencyLayoutNode_.active = false
    }

    private async onTimeStoneUseJackpot(evts: number[], addAry: ConditionObj[], ids: number[]) {
        const promises = []
        for (const index of ids) {
            const base = ids.length == 1 ? this.oneNode_ : this.tenNode_.children[index]
            const cond = addAry[index]
            base.Swih("FrameIconNum")
            base.Child("FrameIconNum", FrameIconNum).init(cond, this.getTag())

        }
        await Promise.all(promises)
        await ut.wait(.5, this)
        this.stopActBottom(this._waitResolve)
    }
}
