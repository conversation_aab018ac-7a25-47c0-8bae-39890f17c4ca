import EventType from "../../../common/event/EventType"
import { gameHelper } from "../../../common/helper/GameHelper"
import { viewHelper } from "../../../common/helper/ViewHelper"
import { ExtractData } from "../../../model/jackpot/JackpotModel"


const { ccclass, property } = cc._decorator

@ccclass
export default class TimeStoneBtnCmpt extends cc.Component {
    private type: proto.TimeStoneEvent = proto.TimeStoneEvent.TypeNone
    private _canTouch: boolean = false

    protected onLoad(): void {
        this._canTouch = false
        this.node.off("click")
    }

    protected start() {
        this.playEnter()
    }


    private async playEnter() {
        const lvNode = this.node.Child("level")
        lvNode.opacity = 0
        const sk = this.node.Child("sp", sp.Skeleton)
        await sk.playAnimation("chuxian", false)
        sk.playAnimation("daiji", true)
        lvNode.setLocaleKey("common_guiText_11", gameHelper.timeStone.getLv())
        cc.Tween.stopAllByTarget(lvNode)
        cc.tween(lvNode).to(0.5, { opacity: 255 }).start()
        this._canTouch = true
    }

    public initJackpot(ary: ExtractData[]) {
        this.type = proto.TimeStoneEvent.TypeJackpot
        this.node.off("click")
        this.node.on("click", () => {
            if (ary.length == 1) {
                const cost = assetsMgr.getJsonData<any>("TimeStoneEvent", proto.TimeStoneEvent.TypeJackpot).cost
                return viewHelper.showPnl("timeStone/TimeStoneUseCommonPnl", {
                    desc: "timeStone_guiText_11", cost, callback: async () =>
                        await eventCenter.req(EventType.TIMESTONE_USE, [0], proto.TimeStoneEvent.TypeJackpot, [0])
                })
            }
            viewHelper.showPnl("timeStone/TimeStoneUseJackpotPnl", ary)
        })
    }
}